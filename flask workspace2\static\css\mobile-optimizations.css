/* Mobile-First Optimizations for GRS Wall Designer */

/* Table Responsiveness */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

table {
    width: 100%;
    border-collapse: collapse;
}

@media (max-width: 768px) {
    /* Stack table cells vertically on mobile */
    .mobile-stack table,
    .mobile-stack thead,
    .mobile-stack tbody,
    .mobile-stack th,
    .mobile-stack td,
    .mobile-stack tr {
        display: block;
    }
    
    .mobile-stack thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }
    
    .mobile-stack tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 8px;
        background: white;
    }
    
    .mobile-stack td {
        border: none;
        position: relative;
        padding: 8px 8px 8px 50%;
        text-align: left;
    }
    
    .mobile-stack td:before {
        content: attr(data-label) ": ";
        position: absolute;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        font-weight: bold;
        color: #333;
    }
}

/* Form Optimizations */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }
    
    .form-row .col {
        width: 100%;
        margin-bottom: 15px;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .input-group-text {
        border-radius: 0.375rem 0.375rem 0 0;
        border-bottom: none;
    }
    
    .input-group .form-control {
        border-radius: 0 0 0.375rem 0.375rem;
    }
}

/* Card Optimizations */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
        border-radius: 12px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .card-header {
        padding: 0.75rem 1rem;
        font-size: 1.1rem;
    }
}

/* Button Groups */
@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 8px;
        border-radius: 0.375rem !important;
    }
    
    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
}

/* Navigation Improvements */
@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
        border-bottom: none;
    }
    
    .nav-tabs .nav-link {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 5px;
        text-align: center;
    }
    
    .nav-tabs .nav-link.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
}

/* Modal Optimizations */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .modal-content {
        border-radius: 12px;
    }
    
    .modal-header {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .modal-body {
        padding: 1rem;
        max-height: 60vh;
        overflow-y: auto;
    }
    
    .modal-footer {
        padding: 1rem;
        flex-direction: column;
    }
    
    .modal-footer .btn {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .modal-footer .btn:last-child {
        margin-bottom: 0;
    }
}

/* Alert Optimizations */
@media (max-width: 768px) {
    .alert {
        margin: 10px;
        border-radius: 8px;
        font-size: 14px;
    }
    
    .alert-dismissible .btn-close {
        padding: 0.75rem;
    }
}

/* Utility Classes for Mobile */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
    
    .mobile-text-center {
        text-align: center !important;
    }
    
    .mobile-no-padding {
        padding: 0 !important;
    }
    
    .mobile-small-padding {
        padding: 0.5rem !important;
    }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
    .btn:hover {
        transform: none;
    }
    
    .btn:active {
        transform: scale(0.98);
    }
    
    .card:hover {
        transform: none;
    }
    
    .menu-item:hover {
        background-color: transparent;
    }
    
    .menu-item:active {
        background-color: rgba(37, 99, 235, 0.1);
    }
}

/* Prevent zoom on input focus (iOS) */
@media screen and (max-width: 768px) {
    input[type="text"],
    input[type="number"],
    input[type="email"],
    input[type="password"],
    textarea,
    select {
        font-size: 16px !important;
    }
}

/* Improve scrolling performance */
.scroll-container {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

/* Fix for iOS Safari bottom bar */
@supports (-webkit-touch-callout: none) {
    .ios-safe-area {
        padding-bottom: env(safe-area-inset-bottom);
    }
}
