/* Run Analysis Page Styles */
.run-analysis-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.analysis-header {
  text-align: center;
  margin-bottom: 3rem;
}

.analysis-header h1 {
  color: #2563eb;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.analysis-description {
  font-size: 1.1rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.analysis-workflow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.workflow-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.workflow-step:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.step-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: #2563eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.step-content h3 {
  color: #1f2937;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.step-content p {
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

.analysis-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.analysis-card {
  padding: 3rem;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.analysis-card h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.analysis-card p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.cta-button {
  background: white;
  color: #2563eb;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cta-button:hover {
  background: #f1f5f9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.analysis-results-section {
  padding: 2rem;
  border-top: 1px solid #e2e8f0;
}

.analysis-results-section h3 {
  color: #059669;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.result-buttons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.result-btn {
  padding: 1rem 1.5rem;
  font-weight: 600;
  border-radius: 8px;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
}

.result-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-decoration: none;
}

.satisfaction-section {
  text-align: center;
  margin-bottom: 1.5rem;
}

.satisfaction-label {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  color: #374151;
  cursor: pointer;
}

.generate-report-section {
  text-align: center;
}

.report-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.report-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.error-message {
  margin-top: 1rem;
  border-radius: 8px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .run-analysis-container {
    padding: 1rem;
  }
  
  .analysis-header h1 {
    font-size: 2rem;
  }
  
  .analysis-workflow {
    grid-template-columns: 1fr;
  }
  
  .workflow-step {
    padding: 1rem;
  }
  
  .analysis-card {
    padding: 2rem 1rem;
  }
  
  .analysis-card h2 {
    font-size: 1.5rem;
  }
  
  .result-buttons-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .analysis-header h1 {
    font-size: 1.75rem;
  }
  
  .analysis-card p {
    font-size: 1rem;
  }
  
  .cta-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}
