{% extends "base.html" %} {% block page_title %}Home - GRS Wall Designer{%
endblock %} {% block content %}
<div class="home-container">
  <div class="hero">
    <h1>Welcome to GRS Wall Designer</h1>
    <p class="tagline">
      Pioneering Geosynthetic Reinforced Soil (GRS) Wall Design Solutions as per
      IS 18591:2024
    </p>
  </div>

  <div class="features">
    <div class="feature">
      <i class="fas fa-cogs"></i>
      <h2>IS 18591:2024 Compliant Design</h2>
      <p>
        Our software adheres to the latest Indian Standard IS 18591:2024 for
        Geosynthetic Reinforced Soil Structures, ensuring compliance with the
        Load and Resistance Factor Design (LRFD) philosophy.
      </p>
    </div>
    <div class="feature">
      <i class="fas fa-project-diagram"></i>
      <h2>Interactive Geometry Visualization</h2>
      <p>
        Visualize your GRS wall designs in real-time with our interactive
        modeling tools. Adjust parameters and see instant updates for precise
        design optimization.
      </p>
    </div>
    <div class="feature">
      <i class="fas fa-file-alt"></i>
      <h2>Professional Report Generation</h2>
      <p>
        Generate comprehensive, concise, and professional reports tailored for
        project submissions. All inputs, and results are presented in a clear
        and structured format.
      </p>
    </div>
    <div class="feature">
      <i class="fas fa-users"></i>
      <h2>User-Centric Interface</h2>
      <p>
        Designed with engineers in mind, our intuitive interface ensures
        seamless navigation and usability for professionals at all experience
        levels.
      </p>
    </div>
    <div class="feature">
      <i class="fas fa-globe-asia"></i>
      <h2>Make in India Initiative</h2>
      <p>
        Proudly developed in India, our software supports the nation's vision of
        self-reliance and technological advancement in civil engineering.
      </p>
    </div>
  </div>

  <div class="workflow-section">
    <div class="cta">
      <h2>Ready to Revolutionize Your GRS Wall Designs?</h2>
      <p>
        Start your journey with the most advanced and IS 18591:2024 compliant GRS
        wall design software. Select an option from the navigation menu to begin
        your GRS wall design journey. After saving all inputs, click below to run
        analysis.
      </p>
      <button id="run-analysis-btn" class="cta-button">Run Analysis</button>
    </div>

    <div id="analysis-results" class="analysis-results-section" style="display: none">
      <h3>Analysis Complete</h3>
      <div id="result-buttons" class="result-buttons-grid">
        <a
          href="{{ url_for('external_stability_results') }}"
          class="btn btn-primary result-btn"
          >External Stability Results</a
        >
        <a
          href="{{ url_for('internal_stability_results') }}"
          class="btn btn-primary result-btn"
          >Internal Stability Results</a
        >
      </div>
      <div class="satisfaction-section">
        <label for="satisfaction-checkbox" class="satisfaction-label">
          <input
            type="checkbox"
            id="satisfaction-checkbox"
            class="form-check-input"
          />
          Are you satisfied with the design configuration?
        </label>
      </div>
      <div id="generate-report-container" class="generate-report-section" style="display: none">
        <div class="alert alert-info mb-3">
          <strong>Note:</strong> For the best report quality, please take a screenshot from the
          <a href="/reinforcementlayout" target="_blank">Reinforcement Layout</a> section before generating the report.
          The screenshot will be automatically included in your report.
        </div>
        <button id="generate-report-btn" class="btn btn-success report-btn">
          Generate Report
        </button>
      </div>
    </div>

    <div
      id="error-message"
      class="alert alert-danger error-message"
      style="display: none"
    ></div>
  </div>
</div>

  <script>
    document.addEventListener("DOMContentLoaded", function () {
      const runAnalysisBtn = document.getElementById("run-analysis-btn");
      const analysisResults = document.getElementById("analysis-results");
      const satisfactionCheckbox = document.getElementById(
        "satisfaction-checkbox"
      );
      const generateReportContainer = document.getElementById(
        "generate-report-container"
      );
      const generateReportBtn = document.getElementById("generate-report-btn");
      const errorMessage = document.getElementById("error-message");

      if (localStorage.getItem("analysisRun") === "true") {
        analysisResults.style.display = "block";
      }

      runAnalysisBtn.addEventListener("click", function () {
        fetch("/run_analysis", { method: "POST" })
          .then((response) => response.json())
          .then((data) => {
            if (data.has_results) {
              analysisResults.style.display = "block";
              localStorage.setItem("analysisRun", "true");
              errorMessage.style.display = "none";
            } else {
              errorMessage.textContent = data.error;
              errorMessage.style.display = "block";
              analysisResults.style.display = "none";
            }
          })
          .catch((error) => {
            console.error("Error:", error);
            errorMessage.textContent =
              "An error occurred while running the analysis.";
            errorMessage.style.display = "block";
          });
      });

      satisfactionCheckbox.addEventListener("change", function () {
        generateReportContainer.style.display = this.checked ? "block" : "none";
      });

      generateReportBtn.addEventListener("click", function () {
        window.location.href = "/generate_report";
      });
    });
  </script>

  {% endblock %}
</div>
