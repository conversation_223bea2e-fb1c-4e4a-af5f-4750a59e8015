<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="theme-color" content="#2563eb" />
    <title>GRS Wall Designer</title>

    <!-- Cache control meta tags -->
    <meta
      http-equiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />

    <!-- Bootstrap 5 for modern UI -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Modern CSS Framework -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/modern-base.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/geometry.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/home.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/project_info.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/reinforcedsoil.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/retainedsoil.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/foundationsoil.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/externalloads.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/reinforcementproperties.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/reinforcementlayout.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/results.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/report.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/run_analysis.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/mobile-optimizations.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/popup.css') }}"
    />

    <!-- Scroll position preservation script -->
    <script>
      // This script runs immediately to prevent visual artifacts
      (function () {
        const savedPosition = localStorage.getItem("sidebarScrollPosition");
        if (savedPosition && parseInt(savedPosition, 10) > 0) {
          // Add CSS to temporarily hide sidebar during initial load
          document.write(
            '<style id="scroll-restore-css">.sidebar { opacity: 0; transition: opacity 0.1s ease; }</style>'
          );
        }
      })();
    </script>
  </head>
  <body>
    {% if request.endpoint in ['login', 'request_access'] %}
    <!-- Login/Request Access Pages - No Sidebar -->
    <div class="auth-container">{% block auth_content %}{% endblock %}</div>
    {% else %}
    <!-- Main Application Layout with Sidebar -->
    <div class="app-container">
      <!-- Sidebar -->
      <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
          <div class="sidebar-brand">
            <i class="fas fa-building"></i>
            <span class="brand-text">GRS Wall Designer</span>
          </div>
          <button class="sidebar-toggle d-lg-none" id="sidebarToggle">
            <i class="fas fa-times"></i>
          </button>
        </div>

        {% if session.get('user') and not session.get('is_admin') %}
        <div class="user-info">
          <div class="user-avatar">
            <i class="fas fa-user"></i>
          </div>
          <div class="user-details">
            <span class="user-name">{{ session['user'] }}</span>
            <span class="user-role">Engineer</span>
          </div>
        </div>
        {% endif %}

        <div class="sidebar-menu">
          {% if session.get('is_admin') %}
          <!-- Admin Menu -->
          <div class="menu-section">
            <div class="menu-title">Administration</div>
            <a
              href="{{ url_for('admin_dashboard') }}"
              class="menu-item {% if request.endpoint == 'admin_dashboard' %}active{% endif %}"
            >
              <i class="fas fa-tachometer-alt"></i>
              <span>Dashboard</span>
            </a>
            <a
              href="{{ url_for('admin_access_requests') }}"
              class="menu-item {% if request.endpoint == 'admin_access_requests' %}active{% endif %}"
            >
              <i class="fas fa-user-check"></i>
              <span>Access Requests</span>
            </a>
          </div>
          {% else %}
          <!-- User Menu -->
          <div class="menu-section">
            <div class="menu-title">Navigation</div>
            <a
              href="{{ url_for('home') }}"
              class="menu-item {% if request.endpoint == 'home' %}active{% endif %}"
            >
              <i class="fas fa-home"></i>
              <span>Home</span>
            </a>
          </div>

          <div class="menu-section">
            <div class="menu-title">Project Setup</div>
            <a
              href="{{ url_for('project_info') }}"
              class="menu-item {% if request.endpoint == 'project_info' %}active{% endif %}"
            >
              <i class="fas fa-info-circle"></i>
              <span>Project Info</span>
              {% if session.get('project_name') %}<i
                class="fas fa-check-circle status-icon saved"
              ></i
              >{% endif %}
            </a>
            <a
              href="{{ url_for('geometry') }}"
              class="menu-item {% if request.endpoint == 'geometry' %}active{% endif %}"
            >
              <i class="fas fa-ruler-combined"></i>
              <span>Geometry</span>
              {% if session.get('geometry_data_saved') %}<i
                class="fas fa-check-circle status-icon saved"
              ></i
              >{% endif %}
            </a>
          </div>

          <div class="menu-section">
            <div class="menu-title">Soil Properties</div>
            <a
              href="{{ url_for('reinforcedsoil') }}"
              class="menu-item {% if request.endpoint == 'reinforcedsoil' %}active{% endif %}"
            >
              <i class="fas fa-layer-group"></i>
              <span>Reinforced Soil</span>
              {% if session.get('soil_density') %}<i
                class="fas fa-check-circle status-icon saved"
              ></i
              >{% endif %}
            </a>
            <a
              href="{{ url_for('retainedsoil') }}"
              class="menu-item {% if request.endpoint == 'retainedsoil' %}active{% endif %}"
            >
              <i class="fas fa-mountain"></i>
              <span>Retained Soil</span>
              {% if session.get('retainedsoil_data_saved') %}<i
                class="fas fa-check-circle status-icon saved"
              ></i
              >{% endif %}
            </a>
            <a
              href="{{ url_for('foundationsoil') }}"
              class="menu-item {% if request.endpoint == 'foundationsoil' %}active{% endif %}"
            >
              <i class="fas fa-cube"></i>
              <span>Foundation Soil</span>
              {% if session.get('foundationsoil_data_saved') %}<i
                class="fas fa-check-circle status-icon saved"
              ></i
              >{% endif %}
            </a>
          </div>

          <div class="menu-section">
            <div class="menu-title">Loads & Reinforcement</div>
            <a
              href="{{ url_for('externalloads') }}"
              class="menu-item {% if request.endpoint == 'externalloads' %}active{% endif %}"
            >
              <i class="fas fa-weight-hanging"></i>
              <span>External Loads</span>
              {% if session.get('externalloads_data') %}<i
                class="fas fa-check-circle status-icon saved"
              ></i
              >{% endif %}
            </a>
            <a
              href="{{ url_for('reinforcementproperties') }}"
              class="menu-item {% if request.endpoint == 'reinforcementproperties' %}active{% endif %}"
            >
              <i class="fas fa-cog"></i>
              <span>Reinforcement Properties</span>
              {% if session.get('reinforcementproperties_data_saved') %}<i
                class="fas fa-check-circle status-icon saved"
              ></i
              >{% endif %}
            </a>
            <a
              href="{{ url_for('reinforcementlayout') }}"
              class="menu-item {% if request.endpoint == 'reinforcementlayout' %}active{% endif %}"
            >
              <i class="fas fa-th-large"></i>
              <span>Reinforcement Layout</span>
              {% if session.get('reinforcementlayout_data_saved') %}<i
                class="fas fa-check-circle status-icon saved"
              ></i
              >{% endif %}
            </a>
          </div>

          <div class="menu-section">
            <div class="menu-title">Analysis</div>
            <a
              href="{{ url_for('run_analysis_page') }}"
              class="menu-item {% if request.endpoint == 'run_analysis_page' %}active{% endif %}"
            >
              <i class="fas fa-play-circle"></i>
              <span>Run Analysis</span>
            </a>
          </div>
          {% endif %}
        </div>

        <div class="sidebar-footer">
          <a href="#" onclick="performLogout()" class="logout-btn">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
          </a>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
          <button class="sidebar-toggle d-lg-none" id="sidebarToggleTop">
            <i class="fas fa-bars"></i>
          </button>
          <div class="page-title">GRS Wall Designer</div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
          {% with messages = get_flashed_messages(with_categories=true) %} {% if
          messages %} {% for category, message in messages %}
          <div class="alert alert-{{ category }} fade show" role="alert">
            {{ message }}
          </div>
          {% endfor %} {% endif %} {% endwith %} {% block content %}{% endblock
          %}
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/validation.js') }}"></script>
    <script src="{{ url_for('static', filename='js/drawingUtils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script src="{{ url_for('static', filename='js/formHandler.js') }}"></script>

    <!-- Sidebar Toggle Script -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const sidebar = document.getElementById("sidebar");
        const sidebarToggle = document.getElementById("sidebarToggle");
        const sidebarToggleTop = document.getElementById("sidebarToggleTop");

        function toggleSidebar() {
          sidebar.classList.toggle("collapsed");
        }

        if (sidebarToggle) {
          sidebarToggle.addEventListener("click", toggleSidebar);
        }

        if (sidebarToggleTop) {
          sidebarToggleTop.addEventListener("click", toggleSidebar);
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener("click", function (event) {
          if (window.innerWidth <= 992) {
            if (
              !sidebar.contains(event.target) &&
              !event.target.closest(".sidebar-toggle")
            ) {
              sidebar.classList.add("collapsed");
            }
          }
        });

        // Sidebar scroll position management
        function saveSidebarScrollPosition() {
          // Try to find the actual scrollable container
          const sidebarMenu = document.querySelector(".sidebar-menu");
          const scrollableElement = sidebarMenu || sidebar;

          if (scrollableElement) {
            localStorage.setItem(
              "sidebarScrollPosition",
              scrollableElement.scrollTop
            );
            console.log(
              "Saved sidebar scroll position:",
              scrollableElement.scrollTop,
              "from element:",
              scrollableElement.className
            );
          }
        }

        function restoreSidebarScrollPosition() {
          // Try to find the actual scrollable container
          const sidebarMenu = document.querySelector(".sidebar-menu");
          const scrollableElement = sidebarMenu || sidebar;

          if (scrollableElement) {
            const savedPosition = localStorage.getItem("sidebarScrollPosition");
            if (savedPosition !== null && parseInt(savedPosition, 10) > 0) {
              // Set scroll position immediately
              scrollableElement.scrollTop = parseInt(savedPosition, 10);

              console.log(
                "Restored sidebar scroll position:",
                savedPosition,
                "to element:",
                scrollableElement.className
              );
            }

            // Ensure any hiding CSS is removed and sidebar is visible
            const scrollRestoreCSS =
              document.getElementById("scroll-restore-css");
            if (scrollRestoreCSS) {
              scrollRestoreCSS.remove();
            }

            // Ensure sidebar is visible
            if (sidebar) {
              sidebar.style.opacity = "1";
            }
          }
        }

        // Restore scroll position on initial page load (fallback)
        restoreSidebarScrollPosition();

        // Save scroll position when navigating away
        window.addEventListener("beforeunload", saveSidebarScrollPosition);

        // AJAX Navigation System
        const sidebarLinks = sidebar.querySelectorAll("a[href]");
        sidebarLinks.forEach((link) => {
          link.addEventListener("click", function (e) {
            const href = link.getAttribute("href");

            // Skip AJAX for special links
            if (
              link.getAttribute("onclick") || // Has onclick handler
              href === "#" || // Hash link
              href.startsWith("javascript:") || // JavaScript link
              href.includes("logout") || // Logout link
              href.includes("admin") || // Admin links
              href.startsWith("http") // External links
            ) {
              // For logout, still save scroll position
              if (href.includes("logout")) {
                saveSidebarScrollPosition();
              }
              return; // Let default behavior handle these
            }

            // Prevent multiple clicks and navigation in progress
            if (isNavigating) {
              e.preventDefault();
              console.log("Navigation in progress, ignoring click");
              return;
            }

            // Check if we're already on this page
            if (window.location.pathname === href) {
              e.preventDefault();
              console.log("Already on this page, ignoring click");
              return;
            }

            // Prevent default page reload
            e.preventDefault();

            // Save scroll position before navigation
            saveSidebarScrollPosition();

            // Load new content via AJAX
            loadPageContent(href);

            // Update browser URL without reload
            history.pushState({ url: href }, "", href);

            // Update active menu item
            updateActiveMenuItem(link);
          });
        });

        // Save scroll position periodically while scrolling
        // Target the actual scrollable element
        const sidebarMenu = document.querySelector(".sidebar-menu");
        const scrollableElement = sidebarMenu || sidebar;

        let scrollTimeout;
        scrollableElement.addEventListener("scroll", function () {
          clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(saveSidebarScrollPosition, 100);
        });

        // Track current AJAX request to prevent multiple simultaneous requests
        let currentAjaxRequest = null;
        let isNavigating = false;

        // Make navigation state available globally
        window.isNavigating = isNavigating;

        // AJAX Content Loading Function
        function loadPageContent(url) {
          const contentArea = document.querySelector(".content-area");
          if (!contentArea) return;

          // Prevent multiple simultaneous requests
          if (isNavigating || currentAjaxRequest) {
            console.log("Navigation already in progress, ignoring request");
            return;
          }

          // Check if we're already on this page
          if (window.location.pathname === url) {
            console.log("Already on this page, ignoring request");
            return;
          }

          isNavigating = true;
          window.isNavigating = true;

          // Show loading indicator
          contentArea.style.opacity = "0.7";
          contentArea.style.pointerEvents = "none";

          currentAjaxRequest = fetch(url)
            .then((response) => {
              if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
              }
              return response.text();
            })
            .then((html) => {
              // Parse the response HTML
              const parser = new DOMParser();
              const doc = parser.parseFromString(html, "text/html");

              // Extract the new content
              const newContentArea = doc.querySelector(".content-area");

              if (newContentArea) {
                // Replace content
                contentArea.innerHTML = newContentArea.innerHTML;

                // Update page title
                const newTitle = doc.querySelector("title");
                if (newTitle) {
                  document.title = newTitle.textContent;
                }

                // Restore content area styles
                contentArea.style.opacity = "1";
                contentArea.style.pointerEvents = "auto";

                // Reinitialize JavaScript for the new content
                reinitializePageScripts();

                console.log("AJAX navigation successful to:", url);
              } else {
                throw new Error("Content area not found in response");
              }
            })
            .catch((error) => {
              console.error("AJAX navigation error:", error);
              // Fallback to normal navigation
              window.location.href = url;
            })
            .finally(() => {
              // Reset navigation state
              isNavigating = false;
              window.isNavigating = false;
              currentAjaxRequest = null;
            });
        }

        // Function to reinitialize JavaScript after AJAX content load
        function reinitializePageScripts() {
          // Execute inline scripts in the new content
          const scripts = document.querySelectorAll(".content-area script");
          scripts.forEach((script) => {
            if (!script.src && script.textContent.trim()) {
              try {
                eval(script.textContent);
                console.log("Executed inline script");
              } catch (e) {
                console.warn("Error executing inline script:", e);
              }
            }
          });

          // Load and execute external scripts
          const scriptTags = document.querySelectorAll(
            ".content-area script[src]"
          );
          let scriptsToLoad = [];

          scriptTags.forEach((script) => {
            const src = script.src;
            if (src && !document.querySelector(`head script[src="${src}"]`)) {
              scriptsToLoad.push(src);
            }
          });

          // Load scripts sequentially to maintain dependencies
          function loadNextScript(index = 0) {
            if (index >= scriptsToLoad.length) {
              // All scripts loaded, trigger final initialization
              setTimeout(() => {
                triggerDOMContentLoaded();
                initializePageSpecificFunctions();
              }, 100);
              return;
            }

            const src = scriptsToLoad[index];
            const newScript = document.createElement("script");
            newScript.src = src;
            newScript.onload = () => {
              console.log("Loaded script:", src);
              loadNextScript(index + 1);
            };
            newScript.onerror = () => {
              console.warn("Failed to load script:", src);
              loadNextScript(index + 1);
            };
            document.head.appendChild(newScript);
          }

          if (scriptsToLoad.length > 0) {
            loadNextScript();
          } else {
            // No new scripts to load, just initialize
            setTimeout(() => {
              triggerDOMContentLoaded();
              initializePageSpecificFunctions();
            }, 100);
          }
        }

        // Trigger DOMContentLoaded event for scripts that depend on it
        function triggerDOMContentLoaded() {
          const event = new Event("DOMContentLoaded", {
            bubbles: true,
            cancelable: true,
          });
          document.dispatchEvent(event);
          console.log("Triggered DOMContentLoaded event");
        }

        // Track last initialization to prevent rapid re-initialization
        let lastInitTime = 0;
        const INIT_DEBOUNCE_MS = 500;

        // Initialize page-specific functions based on current URL
        function initializePageSpecificFunctions() {
          const currentPath = window.location.pathname;
          const now = Date.now();

          // Debounce initialization to prevent rapid-fire calls
          if (now - lastInitTime < INIT_DEBOUNCE_MS) {
            console.log("Initialization debounced, skipping");
            return;
          }

          lastInitTime = now;
          console.log("Initializing functions for path:", currentPath);

          // Small delay to ensure all scripts are ready
          setTimeout(() => {
            // Call specific initialization functions if they exist
            if (
              currentPath === "/geometry" &&
              typeof window.initializeGeometryVisualization === "function"
            ) {
              try {
                console.log("Calling initializeGeometryVisualization...");
                // Reset the initialization flag to allow reinitialization
                window.geometryVisualizationInitialized = false;
                window.initializeGeometryVisualization();
              } catch (e) {
                console.warn("Error initializing geometry visualization:", e);
              }
            } else if (
              currentPath === "/externalloads" &&
              typeof window.initializeExternalLoadsVisualization === "function"
            ) {
              try {
                console.log("Calling initializeExternalLoadsVisualization...");
                window.initializeExternalLoadsVisualization();

                // Also reinitialize external loads specific functions
                initializeExternalLoadsPageFunctions();
              } catch (e) {
                console.warn(
                  "Error initializing external loads visualization:",
                  e
                );
              }
            } else if (currentPath === "/reinforcementlayout") {
              try {
                console.log("Reinitializing reinforcement layout page...");
                // Reinitialize jQuery event handlers for reinforcement layout
                initializeReinforcementLayoutFunctions();

                // Also initialize layout visualization if available
                if (
                  typeof window.initializeLayoutVisualization === "function"
                ) {
                  console.log("Calling initializeLayoutVisualization...");
                  window.initializeLayoutVisualization();
                }
              } catch (e) {
                console.warn("Error initializing reinforcement layout:", e);
              }
            } else if (currentPath === "/reinforcementproperties") {
              try {
                console.log("Reinitializing reinforcement properties page...");
                // Reinitialize jQuery event handlers for reinforcement properties
                initializeReinforcementPropertiesFunctions();
              } catch (e) {
                console.warn("Error initializing reinforcement properties:", e);
              }
            }

            // Re-initialize any form handlers or button listeners
            initializeFormHandlers();

            // Reinitialize standardized form handlers
            if (window.initializeFormHandlers) {
              window.initializeFormHandlers();
            }
          }, 200);
        }

        // Initialize external loads page specific functions
        function initializeExternalLoadsPageFunctions() {
          console.log("Initializing external loads page functions...");

          // Recreate showSection function
          window.showSection = function (sectionId) {
            // Hide all sections and remove active class from all buttons
            document
              .querySelectorAll(".load-input-section")
              .forEach((section) => {
                section.style.display = "none";
              });
            document.querySelectorAll(".tab-button").forEach((button) => {
              button.classList.remove("active");
            });

            // Show selected section and set active class
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
              targetSection.style.display = "block";
            }

            const targetButton = document.querySelector(
              `button[onclick="showSection('${sectionId}')"]`
            );
            if (targetButton) {
              targetButton.classList.add("active");
            }
          };

          // Recreate toggleKhInput function
          window.toggleKhInput = function () {
            const khInput = document.getElementById("seismic_force");
            const checkbox = document.getElementById("use_direct_kh");

            if (khInput && checkbox) {
              khInput.disabled = !checkbox.checked;
              if (checkbox.checked) {
                khInput.focus();
              }
            }
          };

          // Ensure the first tab is active by default
          setTimeout(() => {
            const firstTabButton = document.querySelector(".tab-button");
            if (firstTabButton) {
              firstTabButton.click();
            }
          }, 100);
        }

        // Initialize reinforcement layout page specific functions
        function initializeReinforcementLayoutFunctions() {
          console.log("Initializing reinforcement layout page functions...");

          // Call the global initialization function if available
          if (typeof window.initializeReinforcementLayout === "function") {
            console.log("Calling window.initializeReinforcementLayout...");
            // Reset initialization flag for AJAX navigation
            window.reinforcementLayoutInitialized = false;
            window.initializeReinforcementLayout();
          } else {
            console.warn("window.initializeReinforcementLayout not found");
          }

          // Update reinforcement type dropdowns with latest data from properties
          updateReinforcementTypeDropdowns();

          // Note: Reinforcement layout form has its own event handlers
          // No need to reinitialize here since the page handles its own events
          console.log(
            "Reinforcement layout AJAX reinitialization skipped - form handles its own events"
          );
        }

        // Initialize reinforcement properties page specific functions
        function initializeReinforcementPropertiesFunctions() {
          console.log(
            "Initializing reinforcement properties page functions..."
          );

          // Call the global initialization function if available
          if (typeof window.initializeReinforcementProperties === "function") {
            console.log("Calling window.initializeReinforcementProperties...");
            // Reset initialization flag for AJAX navigation
            window.reinforcementPropertiesInitialized = false;
            window.initializeReinforcementProperties();
          } else {
            console.warn("window.initializeReinforcementProperties not found");
          }

          // Try to call the global load function if available
          if (
            typeof window.loadReinforcementPropertiesFromLocalStorage ===
            "function"
          ) {
            console.log(
              "Calling global loadReinforcementPropertiesFromLocalStorage..."
            );
            window.loadReinforcementPropertiesFromLocalStorage();
          }

          // Also explicitly try to load data from localStorage as a fallback
          setTimeout(() => {
            const storedData = localStorage.getItem("reinforcementData");
            console.log(
              "AJAX: Checking localStorage for reinforcementData:",
              storedData
            );

            if (storedData) {
              try {
                const reinforcementData = JSON.parse(storedData);
                if (
                  Array.isArray(reinforcementData) &&
                  reinforcementData.length > 0
                ) {
                  // Check if we should load from localStorage (improved logic)
                  let shouldLoadFromStorage = false;
                  const currentRows = $("#reinforcementTable tbody tr").length;

                  if (currentRows === 0) {
                    shouldLoadFromStorage = true;
                    console.log(
                      "AJAX: No rows found, loading from localStorage"
                    );
                  } else if (currentRows === 1) {
                    // Check if the single row is empty (server fallback)
                    const firstRow = $("#reinforcementTable tbody tr:first");
                    const typeId = firstRow
                      .find("input[name^='type_id_']")
                      .val();
                    const name = firstRow.find("input[name^='name_']").val();
                    const tult = firstRow.find("input[name^='tult_']").val();

                    if (!typeId && !name && !tult) {
                      shouldLoadFromStorage = true;
                      console.log(
                        "AJAX: Found empty fallback row, loading from localStorage"
                      );
                    }
                  }

                  if (shouldLoadFromStorage) {
                    console.log("AJAX: Loading from localStorage...");
                    $("#reinforcementTable tbody").empty();

                    reinforcementData.forEach((item, index) => {
                      var newRow = `
                    <tr>
                      <td><input type="text" name="type_id_${index}" value="${
                        item.type_id || ""
                      }" required/></td>
                      <td><input type="text" name="name_${index}" value="${
                        item.name || ""
                      }" required/></td>
                      <td><input type="number" step="0.01" min="0" name="tult_${index}" value="${
                        item.tult || ""
                      }" required/></td>
                      <td><input type="number" step="0.01" min="1" name="rfid_${index}" value="${
                        item.rfid || ""
                      }" required/></td>
                      <td><input type="number" step="0.01" min="1" name="rfw_${index}" value="${
                        item.rfw || ""
                      }" required/></td>
                      <td><input type="number" step="0.01" min="1" name="rfcr_${index}" value="${
                        item.rfcr || ""
                      }" required/></td>
                      <td><input type="number" step="0.01" min="1" name="fs_${index}" value="${
                        item.fs || ""
                      }" required/></td>
                      <td><input type="number" step="0.1" min="0" max="90" name="pullout_angle_${index}" value="${
                        item.pullout_angle || ""
                      }" required/></td>
                      <td><input type="number" step="0.1" min="0" max="90" name="sliding_angle_${index}" value="${
                        item.sliding_angle || ""
                      }" required/></td>
                      <td><input type="number" step="0.01" min="0" name="scale_factor_${index}" value="${
                        item.scale_factor || ""
                      }" required/></td>
                      <td><button type="button" class="btn btn-danger btn-sm remove-row">Remove</button></td>
                    </tr>
                  `;
                      $("#reinforcementTable tbody").append(newRow);
                    });

                    console.log(
                      `AJAX: Loaded ${reinforcementData.length} rows from localStorage`
                    );
                  } else {
                    console.log(
                      "AJAX: Server data exists, not loading from localStorage"
                    );
                  }
                }
              } catch (e) {
                console.error("AJAX: Error loading from localStorage:", e);
              }
            }
          }, 300);

          // Ensure jQuery is available
          if (typeof $ !== "undefined") {
            // Note: The Add Row button handler is already defined in the page's script
            // Don't add a duplicate handler here to avoid double row creation
            console.log(
              "AJAX: Skipping Add Row handler - page handles its own events"
            );

            // Reinitialize remove row buttons
            $(document)
              .off("click", ".remove-row")
              .on("click", ".remove-row", function () {
                console.log("Remove row button clicked");
                var rowCount = $("#reinforcementTable tbody tr").length;
                if (rowCount <= 1) {
                  if (typeof showErrorPopup === "function") {
                    showErrorPopup(
                      "Cannot remove the last row. Please keep at least one row."
                    );
                  } else {
                    alert(
                      "Cannot remove the last row. Please keep at least one row."
                    );
                  }
                  return;
                }

                $(this).closest("tr").remove();

                // Save to localStorage
                if (typeof saveToLocalStorage === "function") {
                  saveToLocalStorage();
                }
              });

            console.log("Reinforcement properties functions reinitialized");
          } else {
            console.warn(
              "jQuery not available for reinforcement properties initialization"
            );
          }
        }

        // Function to update reinforcement type dropdowns with latest data
        function updateReinforcementTypeDropdowns() {
          console.log("Updating reinforcement type dropdowns...");

          // Get reinforcement data from localStorage
          const storedReinforcementData =
            localStorage.getItem("reinforcementData");
          if (!storedReinforcementData) {
            console.log("No reinforcement data found in localStorage");
            return;
          }

          const reinforcementData = JSON.parse(storedReinforcementData);
          console.log("Found reinforcement data:", reinforcementData);

          // Find all reinforcement type dropdowns
          const dropdowns = document.querySelectorAll(
            'select[name^="reinforcement_type"]'
          );

          dropdowns.forEach((dropdown) => {
            const currentValue = dropdown.value;

            // Clear existing options
            dropdown.innerHTML =
              '<option value="">Select Reinforcement Type</option>';

            // Add options from localStorage data
            reinforcementData.forEach((item) => {
              if (item.name && item.name.trim() !== "") {
                const option = document.createElement("option");
                option.value = item.name;
                option.textContent = item.name;
                if (currentValue === item.name) {
                  option.selected = true;
                }
                dropdown.appendChild(option);
              }
            });
          });

          console.log(
            `Updated ${dropdowns.length} reinforcement type dropdowns`
          );
        }

        // Initialize form handlers and button listeners
        function initializeFormHandlers() {
          // Don't clone forms - this removes event listeners!
          // Instead, just reinitialize the form handlers
          console.log("Reinitializing form handlers after AJAX navigation");

          // Let the formHandler.js reinitialize itself
          if (window.initializeFormHandlers) {
            try {
              window.initializeFormHandlers();
            } catch (e) {
              console.warn("Error reinitializing form handlers:", e);
            }
          }
        }

        // Update active menu item
        function updateActiveMenuItem(activeLink) {
          // Remove active class from all menu items
          const allMenuItems = sidebar.querySelectorAll(".menu-item");
          allMenuItems.forEach((item) => item.classList.remove("active"));

          // Add active class to clicked item's parent menu-item
          const menuItem = activeLink.closest(".menu-item");
          if (menuItem) {
            menuItem.classList.add("active");
          }
        }

        // Make AJAX functions available globally
        window.loadPageContent = loadPageContent;
        window.updateActiveMenuItem = updateActiveMenuItem;
        window.reinitializePageScripts = reinitializePageScripts;

        // Handle browser back/forward navigation
        window.addEventListener("popstate", function (event) {
          const currentUrl = window.location.pathname;

          if (event.state && event.state.url) {
            // Load the page content for the URL
            loadPageContent(currentUrl);

            // Update active menu item based on current URL
            const activeLink = sidebar.querySelector(`a[href="${currentUrl}"]`);
            if (activeLink) {
              updateActiveMenuItem(activeLink);
            }
          } else {
            // Fallback to page reload for initial page load
            setTimeout(restoreSidebarScrollPosition, 50);
          }
        });
      });
    </script>

    <script>
      // Logout functionality with cookie and cache clearing
      function clearAllCookies() {
        // Get all cookies
        const cookies = document.cookie.split(";");

        // Clear each cookie
        for (let cookie of cookies) {
          const eqPos = cookie.indexOf("=");
          const name =
            eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

          // Delete cookie for current domain
          document.cookie =
            name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
          document.cookie =
            name +
            "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" +
            window.location.hostname;
          document.cookie =
            name +
            "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=." +
            window.location.hostname;

          // Also try with different path variations
          document.cookie =
            name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;";
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;";
        }

        console.log("All cookies cleared");
      }

      function clearBrowserCache() {
        try {
          // Preserve sidebar scroll position before clearing localStorage
          const sidebarScrollPosition = localStorage.getItem(
            "sidebarScrollPosition"
          );

          // Clear localStorage
          if (typeof Storage !== "undefined" && localStorage) {
            localStorage.clear();
            console.log("localStorage cleared");

            // Restore sidebar scroll position after clearing
            if (sidebarScrollPosition !== null) {
              localStorage.setItem(
                "sidebarScrollPosition",
                sidebarScrollPosition
              );
            }
          }

          // Clear sessionStorage
          if (typeof Storage !== "undefined" && sessionStorage) {
            sessionStorage.clear();
            console.log("sessionStorage cleared");
          }

          // Clear IndexedDB (if available)
          if ("indexedDB" in window) {
            indexedDB
              .databases()
              .then((databases) => {
                databases.forEach((db) => {
                  indexedDB.deleteDatabase(db.name);
                });
              })
              .catch((e) => console.log("IndexedDB clear failed:", e));
          }

          // Clear Cache API (if available)
          if ("caches" in window) {
            caches
              .keys()
              .then((names) => {
                names.forEach((name) => {
                  caches.delete(name);
                });
              })
              .catch((e) => console.log("Cache API clear failed:", e));
          }
        } catch (e) {
          console.log("Cache clearing error:", e);
        }
      }

      function performLogout() {
        // Show loading message
        const originalText = event.target.innerHTML;
        event.target.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> Logging out...';
        event.target.style.pointerEvents = "none";

        try {
          // Step 1: Clear cookies
          clearAllCookies();

          // Step 2: Clear browser cache/storage
          clearBrowserCache();

          // Step 3: Call server logout
          fetch('{{ url_for("logout") }}', {
            method: "GET",
            credentials: "same-origin",
          })
            .then((response) => {
              // Step 4: Force page reload to clear any remaining data
              window.location.href = '{{ url_for("login") }}';
            })
            .catch((error) => {
              console.error("Logout error:", error);
              // Even if server logout fails, redirect to login
              window.location.href = '{{ url_for("login") }}';
            });
        } catch (error) {
          console.error("Client-side logout error:", error);
          // Fallback: just redirect to server logout
          window.location.href = '{{ url_for("logout") }}';
        }
      }

      // Alternative function for direct logout link (fallback)
      function directLogout() {
        clearAllCookies();
        clearBrowserCache();
        window.location.href = '{{ url_for("logout") }}';
      }
    </script>
  </body>
</html>
